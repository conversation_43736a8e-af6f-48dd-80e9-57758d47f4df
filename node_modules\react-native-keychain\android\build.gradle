buildscript {
  // Buildscript is evaluated before everything else so we can't use safeExtGet
  def kotlin_version = rootProject.ext.has('kotlinVersion') ? rootProject.ext.get('kotlinVersion') : project.properties['RNKeychain_kotlinVersion']
  repositories {
    google()
    mavenCentral()
  }
  dependencies {
    classpath "com.android.tools.build:gradle:8.3.1"
    classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
  }
}

def safeExtGet(prop, fallback) {
  return rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
}

def isNewArchitectureEnabled() {
  return project.hasProperty("newArchEnabled") && project.newArchEnabled == "true"
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply from: "$projectDir/react-native-helpers.gradle"

if (isNewArchitectureEnabled()) {
  apply plugin: "com.facebook.react"
}

android {
  def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION
  if (agpVersion.tokenize('.')[0].toInteger() >= 7) {
    namespace = "com.oblador.keychain"
    buildFeatures {
      buildConfig true
    }
  }
  compileSdkVersion safeExtGet('compileSdkVersion', project.properties['RNKeychain_compileSdkVersion'].toInteger())

  defaultConfig {
    minSdkVersion safeExtGet('minSdkVersion', project.properties['RNKeychain_minSdkVersion'].toInteger())
    targetSdkVersion safeExtGet('targetSdkVersion', project.properties['RNKeychain_targetSdkVersion'].toInteger())
    buildConfigField "boolean", "IS_NEW_ARCHITECTURE_ENABLED", isNewArchitectureEnabled().toString()
  }

  compileOptions {
    sourceCompatibility JavaVersion.VERSION_17
    targetCompatibility JavaVersion.VERSION_17
  }
}

repositories {
  mavenCentral()
  google()
}

def kotlin_version = safeExtGet('kotlinVersion', project.properties['RNKeychain_kotlinVersion'])

dependencies {
  if (project.ext.shouldConsumeReactNativeFromMavenCentral()) {
    // noinspection GradleDynamicVersion
    implementation 'com.facebook.react:react-android:+'
  } else {
    // noinspection GradleDynamicVersion
    implementation 'com.facebook.react:react-native:+'
  }
  implementation 'androidx.appcompat:appcompat:1.6.1'
  implementation 'androidx.legacy:legacy-support-v4:1.0.0'
  implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"

  // https://mvnrepository.com/artifact/androidx.fragment/fragment-ktx
  // Needed for BiometricPrompt in androidx.biometric
  implementation 'androidx.fragment:fragment-ktx:1.8.6'

  // https://mvnrepository.com/artifact/androidx.biometric/biometric
  implementation 'androidx.biometric:biometric:1.1.0@aar'

  // https://mvnrepository.com/artifact/androidx.lifecycle/lifecycle-viewmodel
  // Needed for BiometricPrompt in androidx.biometric
  implementation "androidx.lifecycle:lifecycle-viewmodel:2.3.1@aar"

  // Used to store encrypted data
  implementation("androidx.datastore:datastore-preferences:1.1.1")
}

if (isNewArchitectureEnabled()) {
  react {
    jsRootDir = file("../src/")
    libraryName = "RNKeychain"
    codegenJavaPackageName = "com.oblador.keychain"
  }
}
