import React from 'react';
import { View, TextInput, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface SearchBarProps {
  placeholder?: string;
  value?: string;
  onChangeText?: (text: string) => void;
  onPress?: () => void;
  editable?: boolean;
}

const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = "Search for products...",
  value,
  onChangeText,
  onPress,
  editable = true
}) => {
  const content = (
    <View className="flex-row items-center bg-neutral-100 rounded-2xl px-4 py-3">
      <Ionicons name="search" size={20} color="#64748b" />
      <TextInput
        className="flex-1 ml-3 text-base text-neutral-800"
        placeholder={placeholder}
        placeholderTextColor="#94a3b8"
        value={value}
        onChangeText={onChangeText}
        editable={editable}
      />
    </View>
  );

  if (onPress && !editable) {
    return (
      <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
        {content}
      </TouchableOpacity>
    );
  }

  return content;
};

export default SearchBar;
