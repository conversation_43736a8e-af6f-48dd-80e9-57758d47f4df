import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface QuantitySelectorProps {
  quantity: number;
  onQuantityChange: (quantity: number) => void;
  min?: number;
  max?: number;
  size?: 'sm' | 'md' | 'lg';
}

const QuantitySelector: React.FC<QuantitySelectorProps> = ({
  quantity,
  onQuantityChange,
  min = 1,
  max = 99,
  size = 'md'
}) => {
  const handleDecrease = () => {
    if (quantity > min) {
      onQuantityChange(quantity - 1);
    }
  };

  const handleIncrease = () => {
    if (quantity < max) {
      onQuantityChange(quantity + 1);
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          container: 'h-8',
          button: 'w-8 h-8',
          text: 'text-sm',
          icon: 16
        };
      case 'lg':
        return {
          container: 'h-12',
          button: 'w-12 h-12',
          text: 'text-lg',
          icon: 24
        };
      default:
        return {
          container: 'h-10',
          button: 'w-10 h-10',
          text: 'text-base',
          icon: 20
        };
    }
  };

  const sizeClasses = getSizeClasses();

  return (
    <View className={`flex-row items-center bg-neutral-100 rounded-lg ${sizeClasses.container}`}>
      <TouchableOpacity
        className={`${sizeClasses.button} items-center justify-center rounded-l-lg ${
          quantity <= min ? 'opacity-50' : ''
        }`}
        onPress={handleDecrease}
        disabled={quantity <= min}
      >
        <Ionicons 
          name="remove" 
          size={sizeClasses.icon} 
          color={quantity <= min ? "#94a3b8" : "#64748b"} 
        />
      </TouchableOpacity>

      <View className="flex-1 items-center justify-center">
        <Text className={`font-semibold text-neutral-800 ${sizeClasses.text}`}>
          {quantity}
        </Text>
      </View>

      <TouchableOpacity
        className={`${sizeClasses.button} items-center justify-center rounded-r-lg ${
          quantity >= max ? 'opacity-50' : ''
        }`}
        onPress={handleIncrease}
        disabled={quantity >= max}
      >
        <Ionicons 
          name="add" 
          size={sizeClasses.icon} 
          color={quantity >= max ? "#94a3b8" : "#64748b"} 
        />
      </TouchableOpacity>
    </View>
  );
};

export default QuantitySelector;
