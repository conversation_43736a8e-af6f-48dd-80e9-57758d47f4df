import React, { useState } from 'react';
import { 
  View, 
  Text, 
  FlatList, 
  TouchableOpacity,
  RefreshControl,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { useQuery } from '@tanstack/react-query';
import { Ionicons } from '@expo/vector-icons';

import { api } from '../services/api';
import { Product } from '../types';
import { useCart } from '../hooks/useCart';

import ProductCard from '../components/ProductCard';
import LoadingSpinner from '../components/LoadingSpinner';
import SearchBar from '../components/SearchBar';
import Button from '../components/Button';

type ProductListRouteProp = RouteProp<{
  ProductList: { categoryId: string; categoryName: string };
}, 'ProductList'>;

const ProductListScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<ProductListRouteProp>();
  const { categoryId, categoryName } = route.params;
  const { addItem } = useCart();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'rating'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Fetch products
  const { 
    data: productsData, 
    isLoading, 
    refetch,
    error 
  } = useQuery({
    queryKey: ['products', categoryId, searchQuery],
    queryFn: () => api.getProducts(categoryId, 1, 50, searchQuery),
  });

  const handleProductPress = (product: Product) => {
    navigation.navigate('ProductDetails' as never, {
      productId: product.id
    } as never);
  };

  const handleAddToCart = (product: Product) => {
    addItem(product);
    Alert.alert(
      'Added to Cart',
      `${product.name} has been added to your cart.`,
      [{ text: 'OK' }]
    );
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  const handleSort = () => {
    Alert.alert(
      'Sort Products',
      'Choose sorting option',
      [
        { text: 'Name A-Z', onPress: () => { setSortBy('name'); setSortOrder('asc'); } },
        { text: 'Name Z-A', onPress: () => { setSortBy('name'); setSortOrder('desc'); } },
        { text: 'Price Low-High', onPress: () => { setSortBy('price'); setSortOrder('asc'); } },
        { text: 'Price High-Low', onPress: () => { setSortBy('price'); setSortOrder('desc'); } },
        { text: 'Rating High-Low', onPress: () => { setSortBy('rating'); setSortOrder('desc'); } },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  };

  const sortedProducts = React.useMemo(() => {
    if (!productsData?.data) return [];
    
    const sorted = [...productsData.data].sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'price':
          comparison = a.price - b.price;
          break;
        case 'rating':
          comparison = a.rating - b.rating;
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });
    
    return sorted;
  }, [productsData?.data, sortBy, sortOrder]);

  const renderProductItem = ({ item, index }: { item: Product; index: number }) => (
    <View className={`w-1/2 ${index % 2 === 0 ? 'pr-2' : 'pl-2'} mb-4`}>
      <ProductCard
        product={item}
        onPress={handleProductPress}
        onAddToCart={handleAddToCart}
      />
    </View>
  );

  const renderHeader = () => (
    <View className="mb-4">
      {/* Search Bar */}
      <SearchBar
        value={searchQuery}
        onChangeText={handleSearch}
        placeholder={`Search in ${categoryName}...`}
      />
      
      {/* Sort and Filter */}
      <View className="flex-row items-center justify-between mt-4">
        <Text className="text-base text-neutral-600">
          {productsData?.data?.length || 0} products found
        </Text>
        
        <TouchableOpacity
          className="flex-row items-center bg-neutral-100 rounded-lg px-3 py-2"
          onPress={handleSort}
        >
          <Ionicons name="funnel" size={16} color="#64748b" />
          <Text className="text-sm text-neutral-700 ml-2">Sort</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderEmpty = () => (
    <View className="flex-1 items-center justify-center py-12">
      <Ionicons name="search" size={64} color="#cbd5e1" />
      <Text className="text-lg font-semibold text-neutral-800 mt-4 mb-2">
        No products found
      </Text>
      <Text className="text-neutral-600 text-center mb-6">
        {searchQuery 
          ? `No products match "${searchQuery}" in ${categoryName}`
          : `No products available in ${categoryName}`
        }
      </Text>
      {searchQuery && (
        <Button
          title="Clear Search"
          onPress={() => setSearchQuery('')}
          variant="outline"
          size="sm"
        />
      )}
    </View>
  );

  const renderError = () => (
    <View className="flex-1 items-center justify-center py-12">
      <Ionicons name="alert-circle" size={64} color="#ef4444" />
      <Text className="text-lg font-semibold text-neutral-800 mt-4 mb-2">
        Failed to load products
      </Text>
      <Text className="text-neutral-600 text-center mb-6">
        Something went wrong while loading products.
      </Text>
      <Button
        title="Try Again"
        onPress={() => refetch()}
        variant="primary"
        size="sm"
      />
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-neutral-50">
      {/* Header */}
      <View className="bg-white px-4 py-4 shadow-sm">
        <View className="flex-row items-center">
          <TouchableOpacity
            className="mr-4"
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="#1e293b" />
          </TouchableOpacity>
          <Text className="text-xl font-bold text-neutral-800 flex-1">
            {categoryName}
          </Text>
        </View>
      </View>

      {/* Content */}
      <View className="flex-1 px-4 pt-4">
        {isLoading ? (
          <LoadingSpinner message="Loading products..." fullScreen />
        ) : error ? (
          renderError()
        ) : (
          <FlatList
            data={sortedProducts}
            renderItem={renderProductItem}
            keyExtractor={(item) => item.id}
            numColumns={2}
            ListHeaderComponent={renderHeader}
            ListEmptyComponent={renderEmpty}
            refreshControl={
              <RefreshControl refreshing={isLoading} onRefresh={refetch} />
            }
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 20 }}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

export default ProductListScreen;
