# GroceEase - Mobile Grocery Shopping App

A fully functional mobile grocery and essentials shopping app built with React Native, TypeScript, NativeWind, and TanStack Query.

## 🎯 Features

### ✅ Implemented Features

- **Onboarding Screen** - Welcome screen with app value proposition
- **Home Screen** - Product categories, search bar, location detection, promotional banners
- **Product Listing** - Grid layout with images, prices, discount badges, sorting and filtering
- **Product Details** - Large images, descriptions, quantity selector, add to cart
- **Shopping Cart** - Cart summary, quantity controls, price breakdown
- **Checkout Flow** - Contact info, delivery address, order confirmation
- **Search** - Real-time product search with debouncing
- **Categories** - Browse products by category
- **Profile & Settings** - User profile, app preferences, theme toggle
- **Location Services** - GPS location detection with permission handling
- **Push Notifications** - Expo notifications setup with test functionality

### 🎨 Design Features

- **Mobile-first responsive design**
- **Psychologically attractive color scheme** (Green for freshness, Yellow/Orange for discounts)
- **Smooth animations and transitions**
- **Consistent UI components**
- **Accessibility-friendly design**

## 🛠 Tech Stack

- **Framework**: React Native with Expo
- **Language**: TypeScript
- **Styling**: NativeWind (Tailwind CSS for React Native)
- **State Management**: 
  - TanStack Query for server state
  - Zustand for client state (cart)
- **Navigation**: React Navigation v6
- **Location**: Expo Location
- **Notifications**: Expo Notifications
- **Icons**: Expo Vector Icons

## 📦 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── Button.tsx
│   ├── CategoryCard.tsx
│   ├── ProductCard.tsx
│   ├── CartItemCard.tsx
│   ├── SearchBar.tsx
│   ├── QuantitySelector.tsx
│   └── LoadingSpinner.tsx
├── screens/            # Screen components
│   ├── OnboardingScreen.tsx
│   ├── HomeScreen.tsx
│   ├── ProductListScreen.tsx
│   ├── ProductDetailsScreen.tsx
│   ├── CartScreen.tsx
│   ├── CheckoutScreen.tsx
│   ├── OrderConfirmationScreen.tsx
│   ├── SearchScreen.tsx
│   ├── CategoriesScreen.tsx
│   ├── ProfileScreen.tsx
│   └── SettingsScreen.tsx
├── navigation/         # Navigation configuration
│   ├── RootNavigator.tsx
│   ├── TabNavigator.tsx
│   └── types.ts
├── services/          # API and data services
│   ├── api.ts         # Mock API functions
│   └── mockData.ts    # Sample data
├── hooks/             # Custom React hooks
│   ├── useCart.ts     # Cart state management
│   ├── useLocation.ts # Location services
│   └── useNotifications.ts # Push notifications
├── types/             # TypeScript type definitions
│   └── index.ts
├── utils/             # Utility functions
│   └── index.ts
└── constants/         # App constants
    └── index.ts
```

## 🚀 Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Expo CLI
- Expo Go app (for testing on device)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd grocease
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm start
   ```

4. **Run on device/simulator**
   - Scan QR code with Expo Go app (iOS/Android)
   - Press `w` for web version
   - Press `a` for Android emulator
   - Press `i` for iOS simulator

## 📱 App Flow

1. **Onboarding** → User sees welcome screen with app benefits
2. **Home** → Browse categories, banners, search products
3. **Product Listing** → View products by category with filters
4. **Product Details** → See detailed product info, add to cart
5. **Cart** → Review items, adjust quantities, see price breakdown
6. **Checkout** → Enter delivery details, place order
7. **Confirmation** → Order success with tracking info

## 🔧 Configuration

### TailwindCSS Setup

The app uses NativeWind for styling. Configuration is in:
- `tailwind.config.js` - Tailwind configuration with custom colors
- `metro.config.js` - Metro bundler configuration for NativeWind
- `global.css` - Global styles

### Mock API

All data is served through mock API functions in `src/services/api.ts`:
- Simulates network delays with `setTimeout`
- Returns realistic data structures
- Easy to replace with real API endpoints

## 🎨 Color Scheme

- **Primary Green**: `#22c55e` - Freshness, organic products
- **Accent Orange**: `#f59e0b` - Discounts, promotions
- **Neutral Grays**: Clean backgrounds and text
- **Status Colors**: Success, warning, error states

## 📊 State Management

### TanStack Query
- Handles all server state (products, categories, user data)
- Automatic caching and background updates
- Loading and error states

### Zustand (Cart)
- Simple, lightweight state management
- Persistent cart state
- Actions for add, remove, update quantities

## 🔔 Notifications

- **Permission handling** - Requests user permission
- **Local notifications** - Test notifications
- **Push token generation** - Ready for remote notifications
- **Notification channels** - Android notification channels

## 📍 Location Services

- **GPS location detection** - Uses device GPS
- **Permission handling** - Requests location permission
- **Address geocoding** - Converts coordinates to addresses
- **Fallback handling** - Graceful degradation if location unavailable

## 🧪 Testing Features

### Notification Testing
1. Go to Profile → Settings
2. Enable notifications (grants permission)
3. Tap "Send Test Notification"
4. Notification appears after 2 seconds

### Location Testing
1. Home screen shows current location
2. Tap location to see details
3. Settings screen shows GPS coordinates
4. Refresh location button updates position

## 🔄 Easy API Integration

The app is designed for easy real API integration:

1. **Replace mock functions** in `src/services/api.ts`
2. **Update base URL** and endpoints
3. **Add authentication** headers
4. **Handle real error responses**
5. **Update data types** if needed

Example:
```typescript
// Replace this:
export const api = {
  getProducts: async () => {
    await delay(1000);
    return { data: mockProducts };
  }
};

// With this:
export const api = {
  getProducts: async () => {
    const response = await fetch('/api/products');
    return response.json();
  }
};
```

## 📝 Future Enhancements

- **Real-time order tracking**
- **Payment gateway integration**
- **User authentication**
- **Wishlist functionality**
- **Product reviews and ratings**
- **Multi-language support**
- **Dark mode implementation**
- **Offline support**

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- **Expo** for the amazing development platform
- **NativeWind** for bringing Tailwind to React Native
- **TanStack Query** for excellent data fetching
- **Unsplash** for beautiful product images
