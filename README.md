# GroceEase - Complete Mobile Grocery Shopping App

A fully functional mobile grocery and essentials shopping app with **complete authentication system** and **order management** built with React Native, TypeScript, NativeWind, and TanStack Query.

## 🎯 Features

### ✅ Implemented Features

#### 🔐 Complete Authentication System
- **Login Screen** - Email/password login with demo credentials
- **Register Screen** - Full registration with validation
- **Forgot Password** - Email-based password reset flow
- **OTP Verification** - 6-digit code verification for multiple purposes
- **Reset Password** - Secure password reset with strength indicator
- **Email Verification** - Email confirmation flow
- **JWT Storage** - Secure token storage with Keychain/Keystore
- **Persistent Login** - Auto-login on app restart
- **Token Refresh** - Automatic token renewal

#### 📦 Order Management System
- **Order History** - Complete order listing with status filtering
- **Order Details** - Detailed order view with progress tracking
- **Order Status Tracking** - Visual progress indicators
- **Reorder Functionality** - Add previous order items to cart
- **Order Filtering** - Filter by status (All, Pending, Delivered, etc.)

#### 🛒 Shopping Features
- **Onboarding Screen** - Welcome screen with app value proposition
- **Home Screen** - Product categories, search bar, location detection, promotional banners
- **Product Listing** - Grid layout with images, prices, discount badges, sorting and filtering
- **Product Details** - Large images, descriptions, quantity selector, add to cart
- **Shopping Cart** - Cart summary, quantity controls, price breakdown
- **Checkout Flow** - Contact info, delivery address, order confirmation
- **Search** - Real-time product search with debouncing
- **Categories** - Browse products by category

#### ⚙️ Additional Features
- **Profile & Settings** - User profile, app preferences, logout
- **Location Services** - GPS location detection with permission handling
- **Push Notifications** - Expo notifications setup with test functionality

### 🎨 Design Features

- **Mobile-first responsive design**
- **Psychologically attractive color scheme** (Green for freshness, Yellow/Orange for discounts)
- **Smooth animations and transitions**
- **Consistent UI components**
- **Accessibility-friendly design**

## 🛠 Tech Stack

- **Framework**: React Native with Expo
- **Language**: TypeScript
- **Styling**: NativeWind (Tailwind CSS for React Native)
- **State Management**:
  - TanStack Query for server state
  - Zustand for client state (cart)
  - React Context for authentication
- **Navigation**: React Navigation v6
- **Storage**:
  - AsyncStorage for general data
  - Keychain/Keystore for secure token storage
- **Location**: Expo Location
- **Notifications**: Expo Notifications
- **Icons**: Expo Vector Icons
- **Authentication**: JWT-based with secure storage

## 📦 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── Button.tsx
│   ├── CategoryCard.tsx
│   ├── ProductCard.tsx
│   ├── CartItemCard.tsx
│   ├── SearchBar.tsx
│   ├── QuantitySelector.tsx
│   └── LoadingSpinner.tsx
├── screens/            # Screen components
│   ├── auth/           # Authentication screens
│   │   ├── LoginScreen.tsx
│   │   ├── RegisterScreen.tsx
│   │   ├── ForgotPasswordScreen.tsx
│   │   ├── OTPVerificationScreen.tsx
│   │   ├── ResetPasswordScreen.tsx
│   │   └── EmailVerificationScreen.tsx
│   ├── OnboardingScreen.tsx
│   ├── HomeScreen.tsx
│   ├── ProductListScreen.tsx
│   ├── ProductDetailsScreen.tsx
│   ├── CartScreen.tsx
│   ├── CheckoutScreen.tsx
│   ├── OrderConfirmationScreen.tsx
│   ├── OrderHistoryScreen.tsx
│   ├── OrderDetailsScreen.tsx
│   ├── SearchScreen.tsx
│   ├── CategoriesScreen.tsx
│   ├── ProfileScreen.tsx
│   └── SettingsScreen.tsx
├── navigation/         # Navigation configuration
│   ├── RootNavigator.tsx
│   ├── TabNavigator.tsx
│   └── types.ts
├── services/          # API and data services
│   ├── api.ts         # Mock API functions
│   ├── authApi.ts     # Authentication API
│   ├── storage.ts     # Secure storage service
│   └── mockData.ts    # Sample data
├── hooks/             # Custom React hooks
│   ├── useAuth.tsx    # Authentication context & hooks
│   ├── useCart.ts     # Cart state management
│   ├── useLocation.ts # Location services
│   └── useNotifications.ts # Push notifications
├── types/             # TypeScript type definitions
│   └── index.ts
├── utils/             # Utility functions
│   └── index.ts
└── constants/         # App constants
    └── index.ts
```

## 🚀 Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Expo CLI
- Expo Go app (for testing on device)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd grocease
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm start
   ```

4. **Run on device/simulator**
   - Scan QR code with Expo Go app (iOS/Android)
   - Press `w` for web version
   - Press `a` for Android emulator
   - Press `i` for iOS simulator

## 📱 App Flow

### Authentication Flow
1. **Login/Register** → User authentication with JWT tokens
2. **Email Verification** → Verify email address (optional)
3. **Forgot Password** → Reset password via OTP verification

### Shopping Flow
1. **Onboarding** → User sees welcome screen with app benefits
2. **Home** → Browse categories, banners, search products
3. **Product Listing** → View products by category with filters
4. **Product Details** → See detailed product info, add to cart
5. **Cart** → Review items, adjust quantities, see price breakdown
6. **Checkout** → Enter delivery details, place order
7. **Confirmation** → Order success with tracking info

### Order Management Flow
1. **Order History** → View all past orders with status filtering
2. **Order Details** → Track order progress and view details
3. **Reorder** → Add previous order items to cart

## 🔐 Authentication Features

### Complete Auth Flow
- **Login** - Email/password with demo credentials (<EMAIL> / password123)
- **Registration** - Full user registration with validation
- **Forgot Password** - Email-based password reset
- **OTP Verification** - 6-digit code verification
- **Password Reset** - Secure password reset with strength indicator
- **Email Verification** - Optional email confirmation

### Security Features
- **JWT Tokens** - Secure authentication tokens
- **Keychain Storage** - Secure token storage on device
- **Auto Refresh** - Automatic token renewal
- **Persistent Login** - Stay logged in across app restarts
- **Secure Logout** - Complete data cleanup on logout

### Demo Credentials
For testing the authentication system:
- **Email**: <EMAIL>
- **Password**: password123

## 📦 Order Management

### Order Features
- **Order History** - View all past orders
- **Status Tracking** - Visual order progress indicators
- **Order Details** - Complete order information
- **Reorder** - Add previous order items to cart
- **Status Filtering** - Filter orders by status

### Order Statuses
- **Pending** - Order placed, awaiting confirmation
- **Confirmed** - Order confirmed by store
- **Preparing** - Order being prepared
- **Out for Delivery** - Order on the way
- **Delivered** - Order successfully delivered
- **Cancelled** - Order cancelled

## 🔧 Configuration

### TailwindCSS Setup

The app uses NativeWind for styling. Configuration is in:
- `tailwind.config.js` - Tailwind configuration with custom colors
- `metro.config.js` - Metro bundler configuration for NativeWind
- `global.css` - Global styles

### Mock API

All data is served through mock API functions in `src/services/api.ts`:
- Simulates network delays with `setTimeout`
- Returns realistic data structures
- Easy to replace with real API endpoints

## 🎨 Color Scheme

- **Primary Green**: `#22c55e` - Freshness, organic products
- **Accent Orange**: `#f59e0b` - Discounts, promotions
- **Neutral Grays**: Clean backgrounds and text
- **Status Colors**: Success, warning, error states

## 📊 State Management

### TanStack Query
- Handles all server state (products, categories, user data)
- Automatic caching and background updates
- Loading and error states

### Zustand (Cart)
- Simple, lightweight state management
- Persistent cart state
- Actions for add, remove, update quantities

## 🔔 Notifications

- **Permission handling** - Requests user permission
- **Local notifications** - Test notifications
- **Push token generation** - Ready for remote notifications
- **Notification channels** - Android notification channels

## 📍 Location Services

- **GPS location detection** - Uses device GPS
- **Permission handling** - Requests location permission
- **Address geocoding** - Converts coordinates to addresses
- **Fallback handling** - Graceful degradation if location unavailable

## 🧪 Testing Features

### Authentication Testing
1. **Login**: Use demo credentials (<EMAIL> / password123)
2. **Register**: Create new account with email verification
3. **Forgot Password**: Test OTP flow (check console for OTP)
4. **Logout**: Test secure logout and data cleanup

### Order Management Testing
1. **Place Orders**: Complete checkout flow to create orders
2. **View History**: Access order history from Profile screen
3. **Order Details**: Tap any order to view detailed information
4. **Reorder**: Test adding previous order items to cart

### Notification Testing
1. Go to Profile → Settings
2. Enable notifications (grants permission)
3. Tap "Send Test Notification"
4. Notification appears after 2 seconds

### Location Testing
1. Home screen shows current location
2. Tap location to see details
3. Settings screen shows GPS coordinates
4. Refresh location button updates position

## 🔄 Easy API Integration

The app is designed for easy real API integration:

1. **Replace mock functions** in `src/services/api.ts`
2. **Update base URL** and endpoints
3. **Add authentication** headers
4. **Handle real error responses**
5. **Update data types** if needed

Example:
```typescript
// Replace this:
export const api = {
  getProducts: async () => {
    await delay(1000);
    return { data: mockProducts };
  }
};

// With this:
export const api = {
  getProducts: async () => {
    const response = await fetch('/api/products');
    return response.json();
  }
};
```

## 📝 Future Enhancements

- **Real-time order tracking** with live updates
- **Payment gateway integration** (Stripe, PayPal, Apple Pay)
- **Social authentication** (Google, Facebook, Apple)
- **Biometric authentication** (Face ID, Touch ID)
- **Wishlist functionality**
- **Product reviews and ratings**
- **Multi-language support**
- **Dark mode implementation** (UI toggle already exists)
- **Offline support** with data synchronization
- **Push notifications** for order updates
- **Loyalty program** and rewards system
- **Advanced search** with filters and suggestions

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- **Expo** for the amazing development platform
- **NativeWind** for bringing Tailwind to React Native
- **TanStack Query** for excellent data fetching
- **Unsplash** for beautiful product images
