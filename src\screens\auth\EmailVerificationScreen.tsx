import React, { useState } from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  ScrollView,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

import { useAuth } from '../../hooks/useAuth';
import Button from '../../components/Button';

type EmailVerificationRouteProp = RouteProp<{
  EmailVerification: { email: string };
}, 'EmailVerification'>;

const EmailVerificationScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<EmailVerificationRouteProp>();
  const { email } = route.params;
  const { resendOTP } = useAuth();
  
  const [isResending, setIsResending] = useState(false);

  const handleVerifyEmail = () => {
    navigation.navigate('OTPVerification' as never, {
      email,
      type: 'email_verification'
    } as never);
  };

  const handleResendEmail = async () => {
    setIsResending(true);
    try {
      const result = await resendOTP(email, 'email_verification');
      
      if (result.success) {
        Alert.alert('Email Sent', 'A new verification email has been sent to your email address');
      } else {
        Alert.alert('Error', result.message);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to resend verification email. Please try again.');
    } finally {
      setIsResending(false);
    }
  };

  const handleSkipForNow = () => {
    Alert.alert(
      'Skip Email Verification',
      'You can verify your email later from your profile settings. Some features may be limited until verification.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Skip', 
          onPress: () => navigation.navigate('Main' as never)
        }
      ]
    );
  };

  const handleChangeEmail = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        <View className="px-6 py-8">
          {/* Header */}
          <View className="items-center mb-8">
            <View className="w-24 h-24 bg-blue-500 rounded-2xl items-center justify-center mb-6">
              <Ionicons name="mail" size={48} color="#ffffff" />
            </View>
            <Text className="text-3xl font-bold text-neutral-800 mb-2">
              Verify Your Email
            </Text>
            <Text className="text-base text-neutral-600 text-center">
              We've sent a verification link to
            </Text>
            <Text className="text-base font-semibold text-neutral-800 mt-1">
              {email}
            </Text>
          </View>

          {/* Instructions */}
          <View className="bg-blue-50 rounded-2xl p-6 border border-blue-200 mb-8">
            <Text className="text-lg font-semibold text-blue-800 mb-4">
              What's next?
            </Text>
            <View className="space-y-3">
              <View className="flex-row items-start">
                <View className="w-6 h-6 bg-blue-500 rounded-full items-center justify-center mr-3 mt-0.5">
                  <Text className="text-xs font-bold text-white">1</Text>
                </View>
                <Text className="text-sm text-blue-700 flex-1">
                  Check your email inbox for a verification message
                </Text>
              </View>
              <View className="flex-row items-start">
                <View className="w-6 h-6 bg-blue-500 rounded-full items-center justify-center mr-3 mt-0.5">
                  <Text className="text-xs font-bold text-white">2</Text>
                </View>
                <Text className="text-sm text-blue-700 flex-1">
                  Click the verification link or enter the code below
                </Text>
              </View>
              <View className="flex-row items-start">
                <View className="w-6 h-6 bg-blue-500 rounded-full items-center justify-center mr-3 mt-0.5">
                  <Text className="text-xs font-bold text-white">3</Text>
                </View>
                <Text className="text-sm text-blue-700 flex-1">
                  Your email will be verified and you can access all features
                </Text>
              </View>
            </View>
          </View>

          {/* Action Buttons */}
          <View className="space-y-4">
            {/* Verify Email Button */}
            <Button
              title="Enter Verification Code"
              onPress={handleVerifyEmail}
              size="lg"
              fullWidth
            />

            {/* Resend Email Button */}
            <Button
              title={isResending ? "Sending..." : "Resend Verification Email"}
              onPress={handleResendEmail}
              variant="outline"
              size="lg"
              fullWidth
              loading={isResending}
            />
          </View>

          {/* Help Section */}
          <View className="bg-neutral-50 rounded-2xl p-6 border border-neutral-200 mt-8">
            <View className="flex-row items-center mb-3">
              <Ionicons name="help-circle" size={20} color="#64748b" />
              <Text className="text-base font-semibold text-neutral-800 ml-2">
                Need Help?
              </Text>
            </View>
            <Text className="text-sm text-neutral-600 mb-4">
              If you don't see the email in your inbox, please check your spam or junk folder. 
              The email should arrive within a few minutes.
            </Text>
            <View className="space-y-2">
              <TouchableOpacity onPress={handleChangeEmail}>
                <Text className="text-primary-600 font-semibold">
                  • Change email address
                </Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => Alert.alert('Contact Support', 'Email: <EMAIL>\nPhone: +****************')}>
                <Text className="text-primary-600 font-semibold">
                  • Contact customer support
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Skip Option */}
          <View className="items-center mt-8">
            <TouchableOpacity onPress={handleSkipForNow}>
              <Text className="text-neutral-500 font-medium">
                Skip for now
              </Text>
            </TouchableOpacity>
            <Text className="text-xs text-neutral-400 text-center mt-2">
              You can verify your email later from your profile settings
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default EmailVerificationScreen;
