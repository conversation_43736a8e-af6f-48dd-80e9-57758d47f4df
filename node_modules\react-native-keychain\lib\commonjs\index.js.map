{"version": 3, "names": ["_reactNative", "require", "_enums", "Object", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "_exportNames", "exports", "defineProperty", "enumerable", "get", "_normalizeOptions", "_types", "RNKeychainManager", "NativeModules", "setGenericPassword", "username", "password", "options", "setGenericPasswordForOptions", "normalize<PERSON>uth<PERSON>rompt", "getGenericPassword", "getGenericPasswordForOptions", "hasGenericPassword", "hasGenericPasswordForOptions", "resetGenericPassword", "resetGenericPasswordForOptions", "getAllGenericPasswordServices", "hasInternetCredentials", "hasInternetCredentialsForOptions", "setInternetCredentials", "server", "setInternetCredentialsForServer", "getInternetCredentials", "getInternetCredentialsForServer", "resetInternetCredentials", "resetInternetCredentialsForOptions", "getSupportedBiometryType", "Promise", "resolve", "requestSharedWebCredentials", "Platform", "OS", "reject", "Error", "setSharedWebCredentials", "setSharedWebCredentialsForServer", "canImplyAuthentication", "canCheckAuthentication", "getSecurityLevel", "isPasscodeAuthAvailable", "_default", "default", "SECURITY_LEVEL", "ACCESSIBLE", "ACCESS_CONTROL", "AUTHENTICATION_TYPE", "BIOMETRY_TYPE", "STORAGE_TYPE"], "sourceRoot": "../../src", "sources": ["index.ts"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAiXAE,MAAA,CAAAC,IAAA,CAAAF,MAAA,EAAAG,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAJ,MAAA,CAAAI,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAZ,MAAA,CAAAI,GAAA;IAAA;EAAA;AAAA;AA9VA,IAAAS,iBAAA,GAAAd,OAAA;AA+VA,IAAAe,MAAA,GAAAf,OAAA;AAAAE,MAAA,CAAAC,IAAA,CAAAY,MAAA,EAAAX,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAU,MAAA,CAAAV,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAE,MAAA,CAAAV,GAAA;IAAA;EAAA;AAAA;AA7VA,MAAM;EAAEW;AAAkB,CAAC,GAAGC,0BAAa;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,kBAAkBA,CAChCC,QAAgB,EAChBC,QAAgB,EAChBC,OAAoB,EACK;EACzB,OAAOL,iBAAiB,CAACM,4BAA4B,CACnD,IAAAC,qCAAmB,EAACF,OAAO,CAAC,EAC5BF,QAAQ,EACRC,QACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASI,kBAAkBA,CAChCH,OAAoB,EACc;EAClC,OAAOL,iBAAiB,CAACS,4BAA4B,CACnD,IAAAF,qCAAmB,EAACF,OAAO,CAC7B,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASK,kBAAkBA,CAACL,OAAqB,EAAoB;EAC1E,OAAOL,iBAAiB,CAACW,4BAA4B,CAACN,OAAO,CAAC;AAChE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASO,oBAAoBA,CAACP,OAAqB,EAAoB;EAC5E,OAAOL,iBAAiB,CAACa,8BAA8B,CAACR,OAAO,CAAC;AAClE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASS,6BAA6BA,CAC3CT,OAAuB,EACJ;EACnB,OAAOL,iBAAiB,CAACc,6BAA6B,CAACT,OAAO,CAAC;AACjE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASU,sBAAsBA,CACpCV,OAA6B,EACX;EAClB,OAAOL,iBAAiB,CAACgB,gCAAgC,CAACX,OAAO,CAAC;AACpE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASY,sBAAsBA,CACpCC,MAAc,EACdf,QAAgB,EAChBC,QAAgB,EAChBC,OAAoB,EACK;EACzB,OAAOL,iBAAiB,CAACmB,+BAA+B,CACtDD,MAAM,EACNf,QAAQ,EACRC,QAAQ,EACR,IAAAG,qCAAmB,EAACF,OAAO,CAC7B,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASe,sBAAsBA,CACpCF,MAAc,EACdb,OAAoB,EACc;EAClC,OAAOL,iBAAiB,CAACqB,+BAA+B,CACtDH,MAAM,EACN,IAAAX,qCAAmB,EAACF,OAAO,CAC7B,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASiB,wBAAwBA,CAACjB,OAAoB,EAAiB;EAC5E,OAAOL,iBAAiB,CAACuB,kCAAkC,CAAClB,OAAO,CAAC;AACtE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASmB,wBAAwBA,CAAA,EAAkC;EACxE,IAAI,CAACxB,iBAAiB,CAACwB,wBAAwB,EAAE;IAC/C,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC;EAC9B;EAEA,OAAO1B,iBAAiB,CAACwB,wBAAwB,CAAC,CAAC;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASG,2BAA2BA,CAAA,EAEzC;EACA,IAAIC,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACzB,OAAOJ,OAAO,CAACK,MAAM,CACnB,IAAIC,KAAK,CACP,qDAAqDH,qBAAQ,CAACC,EAAE,MAClE,CACF,CAAC;EACH;EACA,OAAO7B,iBAAiB,CAAC2B,2BAA2B,CAAC,CAAC;AACxD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASK,uBAAuBA,CACrCd,MAAc,EACdf,QAAgB,EAChBC,QAAiB,EACF;EACf,IAAIwB,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACzB,OAAOJ,OAAO,CAACK,MAAM,CACnB,IAAIC,KAAK,CACP,iDAAiDH,qBAAQ,CAACC,EAAE,MAC9D,CACF,CAAC;EACH;EACA,OAAO7B,iBAAiB,CAACiC,gCAAgC,CACvDf,MAAM,EACNf,QAAQ,EACRC,QACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS8B,sBAAsBA,CACpC7B,OAAkC,EAChB;EAClB,IAAI,CAACL,iBAAiB,CAACmC,sBAAsB,EAAE;IAC7C,OAAOV,OAAO,CAACC,OAAO,CAAC,KAAK,CAAC;EAC/B;EACA,OAAO1B,iBAAiB,CAACmC,sBAAsB,CAAC9B,OAAO,CAAC;AAC1D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS+B,gBAAgBA,CAC9B/B,OAA6B,EACG;EAChC,IAAI,CAACL,iBAAiB,CAACoC,gBAAgB,EAAE;IACvC,OAAOX,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC;EAC9B;EACA,OAAO1B,iBAAiB,CAACoC,gBAAgB,CAAC/B,OAAO,CAAC;AACpD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASgC,uBAAuBA,CAAA,EAAqB;EAC1D,IAAI,CAACrC,iBAAiB,CAACqC,uBAAuB,EAAE;IAC9C,OAAOZ,OAAO,CAACC,OAAO,CAAC,KAAK,CAAC;EAC/B;EACA,OAAO1B,iBAAiB,CAACqC,uBAAuB,CAAC,CAAC;AACpD;AAIA;AAAA,IAAAC,QAAA,GAAA5C,OAAA,CAAA6C,OAAA,GACe;EACbC,cAAc,EAAdA,qBAAc;EACdC,UAAU,EAAVA,iBAAU;EACVC,cAAc,EAAdA,qBAAc;EACdC,mBAAmB,EAAnBA,0BAAmB;EACnBC,aAAa,EAAbA,oBAAa;EACbC,YAAY,EAAZA,mBAAY;EACZT,gBAAgB;EAChBF,sBAAsB;EACtBV,wBAAwB;EACxBP,sBAAsB;EACtBoB,uBAAuB;EACvBjB,sBAAsB;EACtBE,wBAAwB;EACxBpB,kBAAkB;EAClBM,kBAAkB;EAClBM,6BAA6B;EAC7BF,oBAAoB;EACpBe,2BAA2B;EAC3BK;AACF,CAAC", "ignoreList": []}