import React from 'react';
import { TouchableOpacity, Text, View, Image } from 'react-native';
import { Category } from '../types';

interface CategoryCardProps {
  category: Category;
  onPress: (category: Category) => void;
}

const CategoryCard: React.FC<CategoryCardProps> = ({ category, onPress }) => {
  return (
    <TouchableOpacity
      className="bg-white rounded-2xl p-4 shadow-sm border border-neutral-100 items-center"
      onPress={() => onPress(category)}
      activeOpacity={0.7}
    >
      <View
        className="w-16 h-16 rounded-2xl items-center justify-center mb-3 overflow-hidden"
        style={{ backgroundColor: category.color + '20' }}
      >
        <Image
          source={{ uri: category.image }}
          className="w-12 h-12 rounded-xl"
          resizeMode="cover"
        />
      </View>
      <Text className="text-sm font-semibold text-neutral-800 text-center">
        {category.name}
      </Text>
    </TouchableOpacity>
  );
};

export default CategoryCard;
