{"version": 3, "names": ["NativeModules", "Platform", "ACCESSIBLE", "ACCESS_CONTROL", "AUTHENTICATION_TYPE", "SECURITY_LEVEL", "STORAGE_TYPE", "BIOMETRY_TYPE", "normalize<PERSON>uth<PERSON>rompt", "RNKeychainManager", "setGenericPassword", "username", "password", "options", "setGenericPasswordForOptions", "getGenericPassword", "getGenericPasswordForOptions", "hasGenericPassword", "hasGenericPasswordForOptions", "resetGenericPassword", "resetGenericPasswordForOptions", "getAllGenericPasswordServices", "hasInternetCredentials", "hasInternetCredentialsForOptions", "setInternetCredentials", "server", "setInternetCredentialsForServer", "getInternetCredentials", "getInternetCredentialsForServer", "resetInternetCredentials", "resetInternetCredentialsForOptions", "getSupportedBiometryType", "Promise", "resolve", "requestSharedWebCredentials", "OS", "reject", "Error", "setSharedWebCredentials", "setSharedWebCredentialsForServer", "canImplyAuthentication", "canCheckAuthentication", "getSecurityLevel", "isPasscodeAuthAvailable"], "sourceRoot": "../../src", "sources": ["index.ts"], "mappings": ";;AAAA,SAASA,aAAa,EAAEC,QAAQ,QAAQ,cAAc;AACtD,SACEC,UAAU,EACVC,cAAc,EACdC,mBAAmB,EACnBC,cAAc,EACdC,YAAY,EACZC,aAAa,QACR,YAAS;AAYhB,SAASC,mBAAmB,QAAQ,uBAAoB;AAExD,MAAM;EAAEC;AAAkB,CAAC,GAAGT,aAAa;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASU,kBAAkBA,CAChCC,QAAgB,EAChBC,QAAgB,EAChBC,OAAoB,EACK;EACzB,OAAOJ,iBAAiB,CAACK,4BAA4B,CACnDN,mBAAmB,CAACK,OAAO,CAAC,EAC5BF,QAAQ,EACRC,QACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,kBAAkBA,CAChCF,OAAoB,EACc;EAClC,OAAOJ,iBAAiB,CAACO,4BAA4B,CACnDR,mBAAmB,CAACK,OAAO,CAC7B,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,kBAAkBA,CAACJ,OAAqB,EAAoB;EAC1E,OAAOJ,iBAAiB,CAACS,4BAA4B,CAACL,OAAO,CAAC;AAChE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASM,oBAAoBA,CAACN,OAAqB,EAAoB;EAC5E,OAAOJ,iBAAiB,CAACW,8BAA8B,CAACP,OAAO,CAAC;AAClE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASQ,6BAA6BA,CAC3CR,OAAuB,EACJ;EACnB,OAAOJ,iBAAiB,CAACY,6BAA6B,CAACR,OAAO,CAAC;AACjE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASS,sBAAsBA,CACpCT,OAA6B,EACX;EAClB,OAAOJ,iBAAiB,CAACc,gCAAgC,CAACV,OAAO,CAAC;AACpE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASW,sBAAsBA,CACpCC,MAAc,EACdd,QAAgB,EAChBC,QAAgB,EAChBC,OAAoB,EACK;EACzB,OAAOJ,iBAAiB,CAACiB,+BAA+B,CACtDD,MAAM,EACNd,QAAQ,EACRC,QAAQ,EACRJ,mBAAmB,CAACK,OAAO,CAC7B,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASc,sBAAsBA,CACpCF,MAAc,EACdZ,OAAoB,EACc;EAClC,OAAOJ,iBAAiB,CAACmB,+BAA+B,CACtDH,MAAM,EACNjB,mBAAmB,CAACK,OAAO,CAC7B,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASgB,wBAAwBA,CAAChB,OAAoB,EAAiB;EAC5E,OAAOJ,iBAAiB,CAACqB,kCAAkC,CAACjB,OAAO,CAAC;AACtE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASkB,wBAAwBA,CAAA,EAAkC;EACxE,IAAI,CAACtB,iBAAiB,CAACsB,wBAAwB,EAAE;IAC/C,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC;EAC9B;EAEA,OAAOxB,iBAAiB,CAACsB,wBAAwB,CAAC,CAAC;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,2BAA2BA,CAAA,EAEzC;EACA,IAAIjC,QAAQ,CAACkC,EAAE,KAAK,KAAK,EAAE;IACzB,OAAOH,OAAO,CAACI,MAAM,CACnB,IAAIC,KAAK,CACP,qDAAqDpC,QAAQ,CAACkC,EAAE,MAClE,CACF,CAAC;EACH;EACA,OAAO1B,iBAAiB,CAACyB,2BAA2B,CAAC,CAAC;AACxD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,uBAAuBA,CACrCb,MAAc,EACdd,QAAgB,EAChBC,QAAiB,EACF;EACf,IAAIX,QAAQ,CAACkC,EAAE,KAAK,KAAK,EAAE;IACzB,OAAOH,OAAO,CAACI,MAAM,CACnB,IAAIC,KAAK,CACP,iDAAiDpC,QAAQ,CAACkC,EAAE,MAC9D,CACF,CAAC;EACH;EACA,OAAO1B,iBAAiB,CAAC8B,gCAAgC,CACvDd,MAAM,EACNd,QAAQ,EACRC,QACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS4B,sBAAsBA,CACpC3B,OAAkC,EAChB;EAClB,IAAI,CAACJ,iBAAiB,CAACgC,sBAAsB,EAAE;IAC7C,OAAOT,OAAO,CAACC,OAAO,CAAC,KAAK,CAAC;EAC/B;EACA,OAAOxB,iBAAiB,CAACgC,sBAAsB,CAAC5B,OAAO,CAAC;AAC1D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS6B,gBAAgBA,CAC9B7B,OAA6B,EACG;EAChC,IAAI,CAACJ,iBAAiB,CAACiC,gBAAgB,EAAE;IACvC,OAAOV,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC;EAC9B;EACA,OAAOxB,iBAAiB,CAACiC,gBAAgB,CAAC7B,OAAO,CAAC;AACpD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS8B,uBAAuBA,CAAA,EAAqB;EAC1D,IAAI,CAAClC,iBAAiB,CAACkC,uBAAuB,EAAE;IAC9C,OAAOX,OAAO,CAACC,OAAO,CAAC,KAAK,CAAC;EAC/B;EACA,OAAOxB,iBAAiB,CAACkC,uBAAuB,CAAC,CAAC;AACpD;AAEA,cAAc,YAAS;AACvB,cAAc,YAAS;AACvB;AACA,eAAe;EACbtC,cAAc;EACdH,UAAU;EACVC,cAAc;EACdC,mBAAmB;EACnBG,aAAa;EACbD,YAAY;EACZoC,gBAAgB;EAChBF,sBAAsB;EACtBT,wBAAwB;EACxBP,sBAAsB;EACtBmB,uBAAuB;EACvBhB,sBAAsB;EACtBE,wBAAwB;EACxBnB,kBAAkB;EAClBK,kBAAkB;EAClBM,6BAA6B;EAC7BF,oBAAoB;EACpBe,2BAA2B;EAC3BI;AACF,CAAC", "ignoreList": []}