import React, { useRef, useEffect } from 'react';
import { 
  View, 
  FlatList, 
  Dimensions,
  NativeScrollEvent,
  NativeSyntheticEvent
} from 'react-native';
import { Banner } from '../types';
import BannerCard from './BannerCard';

interface AutoScrollBannerProps {
  banners: Banner[];
  autoScrollInterval?: number;
  onBannerPress?: (banner: Banner) => void;
}

const { width } = Dimensions.get('window');

const AutoScrollBanner: React.FC<AutoScrollBannerProps> = ({
  banners,
  autoScrollInterval = 3000,
  onBannerPress
}) => {
  const flatListRef = useRef<FlatList>(null);
  const currentIndex = useRef(0);
  const autoScrollTimer = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (banners.length > 1) {
      startAutoScroll();
    }
    
    return () => {
      stopAutoScroll();
    };
  }, [banners.length]);

  const startAutoScroll = () => {
    stopAutoScroll();
    autoScrollTimer.current = setInterval(() => {
      if (flatListRef.current && banners.length > 1) {
        currentIndex.current = (currentIndex.current + 1) % banners.length;
        flatListRef.current.scrollToIndex({
          index: currentIndex.current,
          animated: true,
        });
      }
    }, autoScrollInterval);
  };

  const stopAutoScroll = () => {
    if (autoScrollTimer.current) {
      clearInterval(autoScrollTimer.current);
      autoScrollTimer.current = null;
    }
  };

  const handleScrollBeginDrag = () => {
    stopAutoScroll();
  };

  const handleScrollEndDrag = () => {
    startAutoScroll();
  };

  const handleMomentumScrollEnd = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const contentOffset = event.nativeEvent.contentOffset;
    const index = Math.round(contentOffset.x / width);
    currentIndex.current = index;
  };

  const renderBanner = ({ item }: { item: Banner }) => (
    <View style={{ width: width - 32 }}>
      <BannerCard banner={item} onPress={onBannerPress} />
    </View>
  );

  const renderDots = () => (
    <View className="flex-row justify-center mt-4 space-x-2">
      {banners.map((_, index) => (
        <View
          key={index}
          className={`w-2 h-2 rounded-full ${
            index === currentIndex.current % banners.length
              ? 'bg-primary-500'
              : 'bg-neutral-300'
          }`}
        />
      ))}
    </View>
  );

  if (!banners || banners.length === 0) {
    return null;
  }

  return (
    <View className="px-4">
      <FlatList
        ref={flatListRef}
        data={banners}
        renderItem={renderBanner}
        keyExtractor={(item) => item.id}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScrollBeginDrag={handleScrollBeginDrag}
        onScrollEndDrag={handleScrollEndDrag}
        onMomentumScrollEnd={handleMomentumScrollEnd}
        getItemLayout={(_, index) => ({
          length: width - 32,
          offset: (width - 32) * index,
          index,
        })}
        initialScrollIndex={0}
        onScrollToIndexFailed={(info) => {
          const wait = new Promise(resolve => setTimeout(resolve, 500));
          wait.then(() => {
            flatListRef.current?.scrollToIndex({ index: info.index, animated: true });
          });
        }}
      />
      {banners.length > 1 && renderDots()}
    </View>
  );
};

export default AutoScrollBanner;
