import { 
  LoginCredentials, 
  RegisterData, 
  AuthResponse, 
  ForgotPasswordData, 
  ResetPasswordData, 
  OTPVerificationData,
  AuthUser,
  ApiResponse 
} from '../types';

// Simulate network delay
const delay = (ms: number = 1000) => new Promise(resolve => setTimeout(resolve, ms));

// Mock user database
const mockUsers: AuthUser[] = [
  {
    id: '1',
    email: '<EMAIL>',
    name: '<PERSON>',
    phone: '+****************',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400',
    isEmailVerified: true,
    isPhoneVerified: true,
    createdAt: '2024-01-01T00:00:00Z',
    addresses: [
      {
        id: '1',
        type: 'home',
        street: '123 Main Street',
        city: 'San Francisco',
        state: 'CA',
        zipCode: '94102',
        isDefault: true
      }
    ]
  }
];

// Mock stored passwords (in real app, these would be hashed)
const mockPasswords: Record<string, string> = {
  '<EMAIL>': 'password123'
};

// Mock OTP storage
const mockOTPs: Record<string, { otp: string; expiresAt: number; type: string }> = {};

// Generate mock JWT token
const generateMockToken = (userId: string): string => {
  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
  const payload = btoa(JSON.stringify({ 
    userId, 
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
    iat: Math.floor(Date.now() / 1000)
  }));
  const signature = btoa('mock-signature');
  return `${header}.${payload}.${signature}`;
};

// Generate mock refresh token
const generateRefreshToken = (): string => {
  return btoa(Math.random().toString(36).substr(2) + Date.now().toString(36));
};

// Generate OTP
const generateOTP = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

export const authApi = {
  // Login
  login: async (credentials: LoginCredentials): Promise<ApiResponse<AuthResponse>> => {
    await delay(1500);
    
    const { email, password } = credentials;
    const user = mockUsers.find(u => u.email.toLowerCase() === email.toLowerCase());
    
    if (!user) {
      return {
        data: null as any,
        success: false,
        message: 'User not found. Please check your email address.'
      };
    }
    
    if (mockPasswords[email] !== password) {
      return {
        data: null as any,
        success: false,
        message: 'Invalid password. Please try again.'
      };
    }
    
    const token = generateMockToken(user.id);
    const refreshToken = generateRefreshToken();
    
    return {
      data: {
        user,
        token,
        refreshToken,
        expiresIn: 24 * 60 * 60 // 24 hours in seconds
      },
      success: true,
      message: 'Login successful'
    };
  },

  // Register
  register: async (userData: RegisterData): Promise<ApiResponse<AuthResponse>> => {
    await delay(2000);
    
    const { name, email, phone, password, confirmPassword } = userData;
    
    // Validation
    if (password !== confirmPassword) {
      return {
        data: null as any,
        success: false,
        message: 'Passwords do not match'
      };
    }
    
    if (mockUsers.find(u => u.email.toLowerCase() === email.toLowerCase())) {
      return {
        data: null as any,
        success: false,
        message: 'Email already exists. Please use a different email.'
      };
    }
    
    // Create new user
    const newUser: AuthUser = {
      id: (mockUsers.length + 1).toString(),
      email,
      name,
      phone,
      isEmailVerified: false,
      isPhoneVerified: false,
      createdAt: new Date().toISOString(),
      addresses: []
    };
    
    mockUsers.push(newUser);
    mockPasswords[email] = password;
    
    const token = generateMockToken(newUser.id);
    const refreshToken = generateRefreshToken();
    
    return {
      data: {
        user: newUser,
        token,
        refreshToken,
        expiresIn: 24 * 60 * 60
      },
      success: true,
      message: 'Registration successful. Please verify your email.'
    };
  },

  // Forgot Password
  forgotPassword: async (data: ForgotPasswordData): Promise<ApiResponse<{ message: string }>> => {
    await delay(1000);
    
    const { email } = data;
    const user = mockUsers.find(u => u.email.toLowerCase() === email.toLowerCase());
    
    if (!user) {
      return {
        data: null as any,
        success: false,
        message: 'Email not found. Please check your email address.'
      };
    }
    
    // Generate OTP for password reset
    const otp = generateOTP();
    mockOTPs[email] = {
      otp,
      expiresAt: Date.now() + (10 * 60 * 1000), // 10 minutes
      type: 'password_reset'
    };
    
    console.log(`Password reset OTP for ${email}: ${otp}`); // In real app, send via email
    
    return {
      data: { message: 'Password reset OTP sent to your email' },
      success: true,
      message: 'Password reset OTP sent successfully'
    };
  },

  // Verify OTP
  verifyOTP: async (data: OTPVerificationData): Promise<ApiResponse<{ token?: string }>> => {
    await delay(800);
    
    const { email, otp, type } = data;
    const storedOTP = mockOTPs[email];
    
    if (!storedOTP || storedOTP.type !== type) {
      return {
        data: null as any,
        success: false,
        message: 'Invalid OTP request'
      };
    }
    
    if (Date.now() > storedOTP.expiresAt) {
      delete mockOTPs[email];
      return {
        data: null as any,
        success: false,
        message: 'OTP has expired. Please request a new one.'
      };
    }
    
    if (storedOTP.otp !== otp) {
      return {
        data: null as any,
        success: false,
        message: 'Invalid OTP. Please try again.'
      };
    }
    
    // OTP verified successfully
    delete mockOTPs[email];
    
    if (type === 'password_reset') {
      // Generate temporary token for password reset
      const resetToken = btoa(`${email}:${Date.now()}`);
      return {
        data: { token: resetToken },
        success: true,
        message: 'OTP verified successfully'
      };
    }
    
    return {
      data: {},
      success: true,
      message: 'OTP verified successfully'
    };
  },

  // Reset Password
  resetPassword: async (data: ResetPasswordData): Promise<ApiResponse<{ message: string }>> => {
    await delay(1000);
    
    const { token, password, confirmPassword } = data;
    
    if (password !== confirmPassword) {
      return {
        data: null as any,
        success: false,
        message: 'Passwords do not match'
      };
    }
    
    try {
      const decoded = atob(token);
      const [email] = decoded.split(':');
      
      if (mockPasswords[email]) {
        mockPasswords[email] = password;
        return {
          data: { message: 'Password reset successful' },
          success: true,
          message: 'Password has been reset successfully'
        };
      }
    } catch (error) {
      // Invalid token
    }
    
    return {
      data: null as any,
      success: false,
      message: 'Invalid or expired reset token'
    };
  },

  // Refresh Token
  refreshToken: async (refreshToken: string): Promise<ApiResponse<{ token: string; refreshToken: string }>> => {
    await delay(500);
    
    // In real app, validate refresh token
    const newToken = generateMockToken('1'); // Mock user ID
    const newRefreshToken = generateRefreshToken();
    
    return {
      data: {
        token: newToken,
        refreshToken: newRefreshToken
      },
      success: true,
      message: 'Token refreshed successfully'
    };
  },

  // Get current user
  getCurrentUser: async (token: string): Promise<ApiResponse<AuthUser>> => {
    await delay(500);
    
    // In real app, decode and validate JWT token
    const user = mockUsers[0]; // Mock current user
    
    return {
      data: user,
      success: true,
      message: 'User data retrieved successfully'
    };
  },

  // Resend OTP
  resendOTP: async (email: string, type: string): Promise<ApiResponse<{ message: string }>> => {
    await delay(800);
    
    const otp = generateOTP();
    mockOTPs[email] = {
      otp,
      expiresAt: Date.now() + (10 * 60 * 1000), // 10 minutes
      type
    };
    
    console.log(`New OTP for ${email}: ${otp}`);
    
    return {
      data: { message: 'OTP sent successfully' },
      success: true,
      message: 'New OTP has been sent to your email'
    };
  }
};
