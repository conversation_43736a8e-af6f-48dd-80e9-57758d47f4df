import React from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { CartItem } from '../types';
import { formatPrice } from '../utils';
import QuantitySelector from './QuantitySelector';

interface CartItemCardProps {
  item: CartItem;
  onQuantityChange: (productId: string, quantity: number) => void;
  onRemove: (productId: string) => void;
}

const CartItemCard: React.FC<CartItemCardProps> = ({
  item,
  onQuantityChange,
  onRemove
}) => {
  const { product, quantity } = item;
  const totalPrice = product.price * quantity;

  return (
    <View className="bg-white rounded-2xl p-4 shadow-sm border border-neutral-100 mb-3">
      <View className="flex-row">
        {/* Product Image */}
        <Image
          source={{ uri: product.image }}
          className="w-20 h-20 rounded-xl"
          resizeMode="cover"
        />

        {/* Product Info */}
        <View className="flex-1 ml-4">
          <View className="flex-row justify-between items-start mb-2">
            <Text className="text-base font-semibold text-neutral-800 flex-1 mr-2" numberOfLines={2}>
              {product.name}
            </Text>
            <TouchableOpacity
              onPress={() => onRemove(product.id)}
              className="p-1"
            >
              <Ionicons name="trash-outline" size={18} color="#ef4444" />
            </TouchableOpacity>
          </View>

          <Text className="text-sm text-neutral-600 mb-3">
            {product.unit}
          </Text>

          {/* Price and Quantity */}
          <View className="flex-row justify-between items-center">
            <View>
              <Text className="text-lg font-bold text-neutral-800">
                {formatPrice(totalPrice)}
              </Text>
              <Text className="text-sm text-neutral-500">
                {formatPrice(product.price)} each
              </Text>
            </View>

            {/* Quantity Selector */}
            <View className="w-24">
              <QuantitySelector
                quantity={quantity}
                onQuantityChange={(newQuantity) => onQuantityChange(product.id, newQuantity)}
                size="sm"
              />
            </View>
          </View>

          {/* Product Tags */}
          {product.tags.length > 0 && (
            <View className="flex-row flex-wrap mt-3">
              {product.tags.slice(0, 2).map((tag, index) => (
                <View 
                  key={index}
                  className={`px-2 py-1 rounded-full mr-1 ${
                    tag === 'organic' ? 'bg-primary-100' :
                    tag === 'fresh' ? 'bg-primary-100' :
                    'bg-neutral-100'
                  }`}
                >
                  <Text 
                    className={`text-xs font-medium ${
                      tag === 'organic' ? 'text-primary-700' :
                      tag === 'fresh' ? 'text-primary-700' :
                      'text-neutral-700'
                    }`}
                  >
                    {tag}
                  </Text>
                </View>
              ))}
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

export default CartItemCard;
