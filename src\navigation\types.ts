import { NavigatorScreenParams } from '@react-navigation/native';

export type RootStackParamList = {
  // Auth Screens
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  OTPVerification: { email: string; type: 'email_verification' | 'password_reset' | 'phone_verification' };
  ResetPassword: { token: string };
  EmailVerification: { email: string };

  // App Screens
  Onboarding: undefined;
  Main: NavigatorScreenParams<TabParamList>;
  ProductList: { categoryId: string; categoryName: string };
  ProductDetails: { productId: string };
  Cart: undefined;
  Checkout: undefined;
  OrderConfirmation: { orderId: string };
  OrderHistory: undefined;
  OrderDetails: { orderId: string };
};

export type TabParamList = {
  Home: undefined;
  Categories: undefined;
  Search: undefined;
  Profile: undefined;
};

export type HomeStackParamList = {
  HomeScreen: undefined;
  ProductList: { categoryId: string; categoryName: string };
};

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}
