import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  Image,
  TouchableOpacity,
  Alert,
  Dimensions
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { useQuery } from '@tanstack/react-query';
import { Ionicons } from '@expo/vector-icons';

import { api } from '../services/api';
import { useCart } from '../hooks/useCart';
import { formatPrice, calculateDiscount } from '../utils';

import LoadingSpinner from '../components/LoadingSpinner';
import Button from '../components/Button';
import QuantitySelector from '../components/QuantitySelector';

type ProductDetailsRouteProp = RouteProp<{
  ProductDetails: { productId: string };
}, 'ProductDetails'>;

const { width } = Dimensions.get('window');

const ProductDetailsScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<ProductDetailsRouteProp>();
  const { productId } = route.params;
  const { addItem, items } = useCart();

  const [quantity, setQuantity] = useState(1);

  // Fetch product details
  const {
    data: productData,
    isLoading,
    error
  } = useQuery({
    queryKey: ['product', productId],
    queryFn: () => api.getProduct(productId),
  });

  const product = productData?.data;
  const discount = product?.originalPrice
    ? calculateDiscount(product.originalPrice, product.price)
    : 0;

  // Check if product is already in cart
  const cartItem = items.find(item => item.product.id === productId);
  const currentCartQuantity = cartItem?.quantity || 0;

  const handleAddToCart = () => {
    if (!product) return;

    addItem(product, quantity);
    Alert.alert(
      'Added to Cart',
      `${quantity} x ${product.name} added to your cart.`,
      [
        { text: 'Continue Shopping', style: 'default' },
        {
          text: 'View Cart',
          onPress: () => navigation.navigate('Cart' as never)
        }
      ]
    );
  };

  const handleGoToCart = () => {
    navigation.navigate('Cart' as never);
  };

  if (isLoading) {
    return <LoadingSpinner message="Loading product details..." fullScreen />;
  }

  if (error || !product) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <View className="flex-1 items-center justify-center px-6">
          <Ionicons name="alert-circle" size={64} color="#ef4444" />
          <Text className="text-xl font-bold text-neutral-800 mt-4 mb-2">
            Product Not Found
          </Text>
          <Text className="text-neutral-600 text-center mb-6">
            The product you're looking for doesn't exist or has been removed.
          </Text>
          <Button
            title="Go Back"
            onPress={() => navigation.goBack()}
            variant="primary"
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Product Image */}
        <View className="relative">
          <Image
            source={{ uri: product.image }}
            style={{ width, height: width * 0.8 }}
            resizeMode="cover"
          />

          {/* Discount Badge */}
          {discount > 0 && (
            <View className="absolute top-4 left-4 bg-accent-500 rounded-full px-3 py-2">
              <Text className="text-sm font-bold text-white">
                {discount}% OFF
              </Text>
            </View>
          )}

          {/* Freshness Indicator */}
          {product.tags.includes('fresh') && (
            <View className="absolute top-4 right-4 bg-primary-500 rounded-full p-2">
              <Ionicons name="leaf" size={20} color="#ffffff" />
            </View>
          )}

          {/* Stock Status */}
          {!product.inStock && (
            <View className="absolute inset-0 bg-black bg-opacity-50 items-center justify-center">
              <View className="bg-white rounded-lg px-4 py-2">
                <Text className="text-neutral-800 font-semibold">Out of Stock</Text>
              </View>
            </View>
          )}
        </View>

        {/* Product Info */}
        <View className="px-6 py-6">
          {/* Name and Unit */}
          <Text className="text-2xl font-bold text-neutral-800 mb-2">
            {product.name}
          </Text>
          <Text className="text-base text-neutral-600 mb-4">
            {product.unit}
          </Text>

          {/* Rating */}
          <View className="flex-row items-center mb-4">
            <View className="flex-row items-center mr-4">
              <Ionicons name="star" size={16} color="#fbbf24" />
              <Text className="text-base text-neutral-700 ml-1 font-medium">
                {product.rating}
              </Text>
            </View>
            <Text className="text-sm text-neutral-500">
              ({product.reviewCount} reviews)
            </Text>
          </View>

          {/* Price */}
          <View className="flex-row items-center mb-6">
            <Text className="text-3xl font-bold text-neutral-800">
              {formatPrice(product.price)}
            </Text>
            {product.originalPrice && (
              <Text className="text-lg text-neutral-500 line-through ml-3">
                {formatPrice(product.originalPrice)}
              </Text>
            )}
          </View>

          {/* Description */}
          <View className="mb-6">
            <Text className="text-lg font-semibold text-neutral-800 mb-3">
              Description
            </Text>
            <Text className="text-base text-neutral-700 leading-6">
              {product.description}
            </Text>
          </View>

          {/* Tags */}
          {product.tags.length > 0 && (
            <View className="mb-6">
              <Text className="text-lg font-semibold text-neutral-800 mb-3">
                Features
              </Text>
              <View className="flex-row flex-wrap">
                {product.tags.map((tag, index) => (
                  <View
                    key={index}
                    className={`px-3 py-2 rounded-full mr-2 mb-2 ${
                      tag === 'organic' ? 'bg-primary-100' :
                      tag === 'fresh' ? 'bg-primary-100' :
                      tag === 'healthy' ? 'bg-blue-100' :
                      tag === 'protein' ? 'bg-purple-100' :
                      'bg-neutral-100'
                    }`}
                  >
                    <Text
                      className={`text-sm font-medium ${
                        tag === 'organic' ? 'text-primary-700' :
                        tag === 'fresh' ? 'text-primary-700' :
                        tag === 'healthy' ? 'text-blue-700' :
                        tag === 'protein' ? 'text-purple-700' :
                        'text-neutral-700'
                      }`}
                    >
                      {tag}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* Quantity Selector */}
          {product.inStock && (
            <View className="mb-6">
              <Text className="text-lg font-semibold text-neutral-800 mb-3">
                Quantity
              </Text>
              <View className="w-32">
                <QuantitySelector
                  quantity={quantity}
                  onQuantityChange={setQuantity}
                  size="lg"
                />
              </View>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Bottom Action Bar */}
      <View className="bg-white border-t border-neutral-200 px-6 py-4">
        {product.inStock ? (
          <View className="flex-row space-x-3">
            {currentCartQuantity > 0 && (
              <Button
                title={`View Cart (${currentCartQuantity})`}
                onPress={handleGoToCart}
                variant="outline"
                size="lg"
                style={{ flex: 1 }}
              />
            )}
            <Button
              title={`Add to Cart • ${formatPrice(product.price * quantity)}`}
              onPress={handleAddToCart}
              size="lg"
              style={{ flex: currentCartQuantity > 0 ? 1 : undefined }}
              fullWidth={currentCartQuantity === 0}
            />
          </View>
        ) : (
          <Button
            title="Out of Stock"
            disabled
            size="lg"
            fullWidth
          />
        )}
      </View>
    </SafeAreaView>
  );
};

export default ProductDetailsScreen;
