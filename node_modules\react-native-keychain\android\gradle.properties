org.gradle.jvmargs=-Xmx3g -Dkotlin.daemon.jvm.options\="-Xmx3g" -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.daemon=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Automatically convert third-party libraries to use AndroidX
android.enableJetifier=true

RNKeychain_kotlinVersion=1.8.0
RNKeychain_compileSdkVersion=34
RNKeychain_targetSdkVersion=34
RNKeychain_minSdkVersion=23
