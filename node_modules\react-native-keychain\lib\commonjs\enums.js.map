{"version": 3, "names": ["_reactNative", "require", "RNKeychainManager", "NativeModules", "ACCESSIBLE", "exports", "ACCESS_CONTROL", "AUTHENTICATION_TYPE", "SECURITY_LEVEL", "SECURITY_LEVEL_SECURE_SOFTWARE", "SECURITY_LEVEL_SECURE_HARDWARE", "SECURITY_LEVEL_ANY", "BIOMETRY_TYPE", "STORAGE_TYPE"], "sourceRoot": "../../src", "sources": ["enums.ts"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEA,MAAM;EAAEC;AAAkB,CAAC,GAAGC,0BAAa;;AAE3C;AACA;AACA;AAFA,IAGYC,UAAU,GAAAC,OAAA,CAAAD,UAAA,0BAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAA,OAAVA,UAAU;AAAA;AAetB;AACA;AACA;AAFA,IAGYE,cAAc,GAAAD,OAAA,CAAAC,cAAA,0BAAdA,cAAc;EAAdA,cAAc;EAAdA,cAAc;EAAdA,cAAc;EAAdA,cAAc;EAAdA,cAAc;EAAdA,cAAc;EAAdA,cAAc;EAAA,OAAdA,cAAc;AAAA;AAiB1B;AACA;AACA;AAFA,IAGYC,mBAAmB,GAAAF,OAAA,CAAAE,mBAAA,0BAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAA,OAAnBA,mBAAmB;AAAA;AAO/B;AACA;AACA;AACA;AAHA,IAIYC,cAAc,GAAAH,OAAA,CAAAG,cAAA,aAAdA,cAAc;EAAdA,cAAc,CAAdA,cAAc,sBAENN,iBAAiB,IACjCA,iBAAiB,CAACO,8BAA8B;EAHxCD,cAAc,CAAdA,cAAc,sBAONN,iBAAiB,IACjCA,iBAAiB,CAACQ,8BAA8B;EARxCF,cAAc,CAAdA,cAAc,UAUlBN,iBAAiB,IAAIA,iBAAiB,CAACS,kBAAkB;EAAA,OAVrDH,cAAc;AAAA;AAa1B;AACA;AACA;AAFA,IAGYI,aAAa,GAAAP,OAAA,CAAAO,aAAA,0BAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAA,OAAbA,aAAa;AAAA;AA2BzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA,IAiBYC,YAAY,GAAAR,OAAA,CAAAQ,YAAA,0BAAZA,YAAY;EAAZA,YAAY;EAAZA,YAAY;EAAZA,YAAY;EAAZA,YAAY;EAAA,OAAZA,YAAY;AAAA", "ignoreList": []}