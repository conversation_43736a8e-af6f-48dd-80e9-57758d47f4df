{"version": 3, "names": ["NativeModules", "RNKeychainManager", "ACCESSIBLE", "ACCESS_CONTROL", "AUTHENTICATION_TYPE", "SECURITY_LEVEL", "SECURITY_LEVEL_SECURE_SOFTWARE", "SECURITY_LEVEL_SECURE_HARDWARE", "SECURITY_LEVEL_ANY", "BIOMETRY_TYPE", "STORAGE_TYPE"], "sourceRoot": "../../src", "sources": ["enums.ts"], "mappings": ";;AAAA,SAASA,aAAa,QAAQ,cAAc;AAE5C,MAAM;EAAEC;AAAkB,CAAC,GAAGD,aAAa;;AAE3C;AACA;AACA;AACA,WAAYE,UAAU,0BAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAA,OAAVA,UAAU;AAAA;;AAetB;AACA;AACA;AACA,WAAYC,cAAc,0BAAdA,cAAc;EAAdA,cAAc;EAAdA,cAAc;EAAdA,cAAc;EAAdA,cAAc;EAAdA,cAAc;EAAdA,cAAc;EAAdA,cAAc;EAAA,OAAdA,cAAc;AAAA;;AAiB1B;AACA;AACA;AACA,WAAYC,mBAAmB,0BAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAA,OAAnBA,mBAAmB;AAAA;;AAO/B;AACA;AACA;AACA;AACA,WAAYC,cAAc,aAAdA,cAAc;EAAdA,cAAc,CAAdA,cAAc,sBAENJ,iBAAiB,IACjCA,iBAAiB,CAACK,8BAA8B;EAHxCD,cAAc,CAAdA,cAAc,sBAONJ,iBAAiB,IACjCA,iBAAiB,CAACM,8BAA8B;EARxCF,cAAc,CAAdA,cAAc,UAUlBJ,iBAAiB,IAAIA,iBAAiB,CAACO,kBAAkB;EAAA,OAVrDH,cAAc;AAAA;;AAa1B;AACA;AACA;AACA,WAAYI,aAAa,0BAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAA,OAAbA,aAAa;AAAA;;AA2BzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAYC,YAAY,0BAAZA,YAAY;EAAZA,YAAY;EAAZA,YAAY;EAAZA,YAAY;EAAZA,YAAY;EAAA,OAAZA,YAAY;AAAA", "ignoreList": []}