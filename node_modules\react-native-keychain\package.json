{"name": "react-native-keychain", "version": "10.0.0", "description": "Keychain Access for React Native", "main": "./lib/commonjs/index.js", "module": "./lib/module/index", "types": "./lib/typescript/index.d.ts", "react-native": "./src/index", "source": "./src/index", "files": ["android", "ios/RNKeychainManager", "lib", "src", "RNKeychain.podspec"], "scripts": {"format": "prettier '{,typings/,KeychainExample/}*.{md,js,json,ts,tsx}' --write", "lint": "eslint \"**/*.{js,ts,tsx}\"", "typecheck": "tsc -p tsconfig.json", "prepare": "bob build"}, "workspaces": ["KeychainExample", "website"], "keywords": ["react-native", "react-component", "react-native-component", "react", "mobile", "ios", "android", "keychain"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": ["<PERSON> <<EMAIL>> (https://github.com/DorianMazur)", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/vonovak)"], "homepage": "https://oblador.github.io/react-native-keychain", "bugs": {"url": "https://github.com/oblador/react-native-keychain/issues"}, "typescript": {"definition": "typings/react-native-keychain.d.ts"}, "packageManager": "yarn@3.6.1", "repository": {"type": "git", "url": "git://github.com/oblador/react-native-keychain.git"}, "license": "MIT", "devDependencies": {"@react-native-community/cli": "^15.0.1", "@react-native/eslint-config": "^0.74.84", "@react-native/typescript-config": "^0.74.84", "eslint": "^8.46.0", "eslint-plugin-prettier": "^5.1.3", "prettier": "^3.0.3", "react": "18.3.0", "react-native": "0.77.1", "react-native-builder-bob": "^0.37.0", "typescript": "^5.2.2"}, "volta": {"node": "16.14.2"}, "engines": {"node": ">=16"}, "codegenConfig": {"name": "RNKeychainSpec", "type": "modules", "jsSrcsDir": "src", "android": {"javaPackageName": "com.oblador.keychain"}}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["commonjs", {"esm": true}], ["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json"}]]}}