import React from 'react';
import { TouchableOpacity, Text, ActivityIndicator, TouchableOpacityProps } from 'react-native';

interface ButtonProps extends TouchableOpacityProps {
  title: string;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  fullWidth?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  title,
  variant = 'primary',
  size = 'md',
  loading = false,
  fullWidth = false,
  disabled,
  style,
  ...props
}) => {
  const getButtonClasses = () => {
    let classes = 'rounded-lg flex-row items-center justify-center ';
    
    // Size classes
    if (size === 'sm') {
      classes += 'px-3 py-2 ';
    } else if (size === 'md') {
      classes += 'px-4 py-3 ';
    } else if (size === 'lg') {
      classes += 'px-6 py-4 ';
    }
    
    // Variant classes
    if (variant === 'primary') {
      classes += disabled || loading ? 'bg-primary-300 ' : 'bg-primary-500 ';
    } else if (variant === 'secondary') {
      classes += disabled || loading ? 'bg-neutral-200 ' : 'bg-neutral-100 ';
    } else if (variant === 'outline') {
      classes += 'border-2 ' + (disabled || loading ? 'border-neutral-300 ' : 'border-primary-500 ');
    }
    
    // Full width
    if (fullWidth) {
      classes += 'w-full ';
    }
    
    return classes.trim();
  };

  const getTextClasses = () => {
    let classes = 'font-semibold ';
    
    // Size classes
    if (size === 'sm') {
      classes += 'text-sm ';
    } else if (size === 'md') {
      classes += 'text-base ';
    } else if (size === 'lg') {
      classes += 'text-lg ';
    }
    
    // Variant classes
    if (variant === 'primary') {
      classes += 'text-white ';
    } else if (variant === 'secondary') {
      classes += disabled || loading ? 'text-neutral-400 ' : 'text-neutral-700 ';
    } else if (variant === 'outline') {
      classes += disabled || loading ? 'text-neutral-400 ' : 'text-primary-500 ';
    }
    
    return classes.trim();
  };

  return (
    <TouchableOpacity
      className={getButtonClasses()}
      disabled={disabled || loading}
      style={style}
      {...props}
    >
      {loading && (
        <ActivityIndicator 
          size="small" 
          color={variant === 'primary' ? '#ffffff' : '#22c55e'} 
          style={{ marginRight: 8 }}
        />
      )}
      <Text className={getTextClasses()}>
        {title}
      </Text>
    </TouchableOpacity>
  );
};

export default Button;
